import Link from "next/link";

export default function AdminHome() {
  return (
    <div className="p-8 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Admin Panel</h1>
        <p className="text-gray-600 mt-2">
          Manage your resort system, bookings, and content
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Resort Management */}
        <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <span className="text-2xl">🏨</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 ml-3">
              Resort Management
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Manage resorts, rooms, and availability
          </p>
          <div className="space-y-2">
            <Link
              href="/admin/resorts"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → Manage Resorts
            </Link>
            <Link
              href="/admin/analytics/resorts"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → Resort Analytics
            </Link>
          </div>
        </div>

        {/* Spa & Services */}
        <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-purple-100 rounded-lg">
              <span className="text-2xl">💆</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 ml-3">
              Spa & Services
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Manage spa treatments and wellness services
          </p>
          <div className="space-y-2">
            <Link
              href="/admin/spa"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → Manage Spa Treatments
            </Link>
          </div>
        </div>

        {/* Reviews & Feedback */}
        <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-green-100 rounded-lg">
              <span className="text-2xl">⭐</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 ml-3">
              Reviews & Feedback
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Manage customer reviews and testimonials
          </p>
          <div className="space-y-2">
            <Link
              href="/admin/reviews"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → Manage Reviews
            </Link>
            <Link
              href="/admin/testimonials"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → Manage Testimonials
            </Link>
          </div>
        </div>

        {/* Bookings */}
        <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-orange-100 rounded-lg">
              <span className="text-2xl">📅</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 ml-3">
              Bookings
            </h2>
          </div>
          <p className="text-gray-600 mb-4">View and manage all bookings</p>
          <div className="space-y-2">
            <Link
              href="/admin/bookings"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → View All Bookings
            </Link>
          </div>
        </div>

        {/* Analytics */}
        <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-indigo-100 rounded-lg">
              <span className="text-2xl">📊</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 ml-3">
              Analytics
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            View performance metrics and insights
          </p>
          <div className="space-y-2">
            <Link
              href="/admin/dashboard"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → Main Dashboard
            </Link>
            <Link
              href="/admin/analytics/resorts"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → Resort Analytics
            </Link>
          </div>
        </div>

        {/* Settings */}
        <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-gray-100 rounded-lg">
              <span className="text-2xl">⚙️</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 ml-3">
              Settings
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            System configuration and user management
          </p>
          <div className="space-y-2">
            <Link
              href="/admin/users"
              className="block text-blue-600 hover:text-blue-800 font-medium"
            >
              → User Management
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
